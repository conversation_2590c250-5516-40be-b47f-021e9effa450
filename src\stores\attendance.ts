import { defineStore } from 'pinia'
import { AttendanceService, type LocationData } from 'src/services/attendanceService'
import type { AttendanceRecord } from 'src/types/attendance'

export const useAttendanceStore = defineStore('attendance', {
  state: () =>
    ({
      attendanceRecords: [] as AttendanceRecord[],
      currentUserAttendance: null,
      loading: false,
      error: null as string | null,
      attendanceStatus: [] as string[],
      summaryByRole: {
        present: 0,
        late: 0,
        sickLeave: 0,
        personalLeave: 0,
      } as {
        present: number
        late: number
        sickLeave: number
        personalLeave: number
      },
    }) as {
      attendanceRecords: Array<AttendanceRecord>
      currentUserAttendance: null | {
        id?: number
        date?: string
        clock_in?: string
        clock_out?: string
        user?: {
          id: number
          name?: string
        }
        work_duration?: number
      }
      loading: boolean
      error: string | null
      attendanceStatus: string[]
      summaryByRole: {
        present: number
        late: number
        sickLeave: number
        personalLeave: number
      }
    },

  getters: {
    isUserClockedIn(): boolean {
      return !!this.currentUserAttendance && !this.currentUserAttendance.clock_out
    },

    todayAttendance():
      | {
          id?: number
          date?: string
          clock_in?: string
          clock_out?: string
          user?: {
            id: number
            name?: string
          }
          work_duration?: number
        }
      | undefined {
      const today = new Date().toISOString().split('T')[0]
      return this.attendanceRecords.find((record) => record.date === today)
    },

    totalWorkDuration(): number {
      return this.attendanceRecords.reduce((total, record) => {
        return total + (record.work_duration || 0)
      }, 0)
    },
  },

  actions: {
    // Clock in action
    async clockIn(userId: number, note?: string, location?: LocationData) {
      this.loading = true
      this.error = null
      try {
        const response = await AttendanceService.clockIn(userId, note, location)
        this.currentUserAttendance = response
        await this.fetchUserAttendance(userId)
        return response
      } catch (error) {
        this.error = 'Failed to clock in'
        console.error('Clock in error:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // Clock out action
    async clockOut(userId: number, note?: string, location?: LocationData) {
      this.loading = true
      this.error = null
      try {
        const response = await AttendanceService.clockOut(userId, note, location)
        this.currentUserAttendance = response
        await this.fetchUserAttendance(userId)
        return response
      } catch (error) {
        this.error = 'Failed to clock out'
        console.error('Clock out error:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // Fetch user attendance records
    async fetchUserAttendance(userId: number) {
      this.loading = true
      this.error = null
      try {
        const records = await AttendanceService.getUserAttendance(userId)
        this.attendanceRecords = records

        // Set current attendance if there's an active session
        const activeAttendance = records.find((record) => !record.clock_out)
        if (activeAttendance) {
          this.currentUserAttendance = activeAttendance
        }
      } catch (error) {
        this.error = 'Failed to fetch attendance records'
        console.error('Fetch attendance error:', error)
      } finally {
        this.loading = false
      }
    },

    // Fetch all attendance status
    async fetchAttendanceStatus() {
      this.loading = true
      this.error = null
      try {
        const status = await AttendanceService.getAllAttendanceStatus()
        this.attendanceStatus = status
      } catch (error) {
        this.error = 'Failed to fetch attendance status'
        console.error('Fetch status error:', error)
      } finally {
        this.loading = false
      }
    },

    // Fetch attendance summary by role
    async fetchSummaryByRole() {
      this.loading = true
      this.error = null
      try {
        const summary = await AttendanceService.getAttendanceSummaryByRole()
        this.summaryByRole = summary
      } catch (error) {
        this.error = 'Failed to fetch role summary'
        console.error('Fetch summary error:', error)
      } finally {
        this.loading = false
      }
    },

    // Fetch total attendance by role
    async fetchTotalByRole() {
      this.loading = true
      this.error = null
      try {
        const totalByRole = await AttendanceService.getTotalAttendanceByRole()
        // You might want to store this data in a new state property
        return totalByRole
      } catch (error) {
        this.error = 'Failed to fetch total by role'
        console.error('Fetch total by role error:', error)
      } finally {
        this.loading = false
      }
    },

    // Fetch total attendance by category
    async fetchTotalByCategory() {
      this.loading = true
      this.error = null
      try {
        const totalByCategory = await AttendanceService.getTotalAttendanceByCategory()
        // You might want to store this data in a new state property
        return totalByCategory
      } catch (error) {
        this.error = 'Failed to fetch total by category'
        console.error('Fetch total by category error:', error)
      } finally {
        this.loading = false
      }
    },

    // Reset store state
    resetState() {
      this.attendanceRecords = []
      this.currentUserAttendance = null
      this.loading = false
      this.error = null
      this.attendanceStatus = []
      this.summaryByRole = {
        present: 0,
        late: 0,
        sickLeave: 0,
        personalLeave: 0,
      }
    },

    // Fetch today's attendance for a specific user
    async fetchUserTodayAttendance(userId: number) {
      this.loading = true
      this.error = null
      try {
        const record = await AttendanceService.getUserTodayAttendance(userId)
        // If there's an active record, set it as current
        if (record && record.id) {
          this.currentUserAttendance = record
        }
        return record
      } catch (error) {
        this.error = "Failed to fetch today's attendance"
        console.error("Fetch today's attendance error:", error)
        return null
      } finally {
        this.loading = false
      }
    },

    // Fetch average work time
    async fetchAverageWorkTime() {
      this.loading = true
      this.error = null
      try {
        const averageWorkTime = await AttendanceService.getAverageWorkTime()
        return averageWorkTime
      } catch (error) {
        this.error = 'Failed to fetch average work time'
        console.error('Fetch average work time error:', error)
        return null
      } finally {
        this.loading = false
      }
    },

    // Fetch monthly hours by date range
    async fetchMonthlyHoursByDateRange(startDate: string, endDate: string) {
      this.loading = true
      this.error = null
      try {
        const monthlyHours = await AttendanceService.getMonthlyHoursByDateRange(startDate, endDate)
        return monthlyHours
      } catch (error) {
        this.error = 'Failed to fetch monthly hours'
        console.error('Fetch monthly hours error:', error)
        return null
      } finally {
        this.loading = false
      }
    },
  },
})
