import { api } from 'src/boot/axios'
import type { AttendanceRecord } from 'src/types/attendance'
import type { AttendanceSummary } from 'src/types/attendancesummary'

export interface LocationData {
  latitude: number
  longitude: number
  accuracy?: number
}

export class AttendanceService {
  static path = 'attendance'

  // Clock in for the current user
  static async clockIn(userId: number, note?: string, location?: LocationData) {
    const response = await api.post(`${this.path}/clock-in`, {
      userId,
      note,
      location,
    })
    return response.data
  }

  // Clock out for the current user
  static async clockOut(userId: number, note?: string, location?: LocationData) {
    const response = await api.post(`${this.path}/clock-out`, {
      userId,
      note,
      location,
    })
    return response.data
  }

  // Get attendance records for a specific user
  static async getUserAttendance(userId: number): Promise<AttendanceRecord[]> {
    const response = await api.get(`${this.path}/user/${userId}`)
    return response.data
  }

  // Get all attendance status
  static async getAllAttendanceStatus() {
    const response = await api.get(`${this.path}/status`)
    return response.data
  }

  // Get attendance summary grouped by role
  static async getAttendanceSummaryByRole() {
    const response = await api.get(`${this.path}/summary-by-role`)
    return response.data
  }

  // Get total attendance by role
  static async getTotalAttendanceByRole() {
    const response = await api.get(`${this.path}/total-by-role`)
    return response.data
  }

  // Get total attendance by category
  static async getTotalAttendanceByCategory() {
    const response = await api.get(`${this.path}/total-by-category`)
    return response.data
  }

  // Get today's attendance for a specific user
  static async getUserTodayAttendance(userId: number) {
    const response = await api.get(`${this.path}/today/${userId}`)
    return response.data
  }

  // Get attendance summary by date range
  static async getAttendanceByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<AttendanceSummary[]> {
    const response = await api.post(`${this.path}/date-range`, {
      startDate,
      endDate,
    })
    return response.data
  }

  // Get average work time
  static async getAverageWorkTime() {
    const response = await api.get(`${this.path}/average-work-time`)
    return response.data
  }

  // Get monthly hours by date range
  static async getMonthlyHoursByDateRange(startDate: string, endDate: string) {
    const response = await api.post(`${this.path}/monthly-hours`, {
      startDate,
      endDate,
    })
    return response.data
  }

  // Error handler utility
  private static handleError(error: unknown) {
    console.error('Attendance service error:', error)
    throw error
  }
}
