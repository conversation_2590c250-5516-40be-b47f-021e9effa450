import { ref, computed, watch, type Ref } from 'vue'
import { date } from 'quasar'
import { useAttendanceStore } from 'src/stores/attendance'
import { AttendanceService } from 'src/services/attendanceService'
import type { AttendanceRecord } from 'src/types/attendance'
import type { user } from 'src/types/user'
import { parseTimeForDisplay, calculateWorkHours } from '../utils'
import { DATE_FORMATS, REFRESH_INTERVALS } from '../constants'
import { useLocationValidation } from './useLocationValidation'

export const useAttendance = (currentUser: Ref<user | null>) => {
  // Store instances
  const attendanceStore = useAttendanceStore()

  // Location validation composable
  const locationValidation = useLocationValidation()

  // Reactive state
  const todayAttendanceData = ref<AttendanceRecord | null>(null)
  const loading = ref(false)
  const lastCheckInTime = ref<string | null>(null)
  const lastCheckOutTime = ref<string | null>(null)
  const attendanceRefreshInterval = ref<NodeJS.Timeout | null>(null)

  /**
   * Check if user is currently checked in
   */
  const isCheckedIn = computed(() => {
    if (todayAttendanceData.value) {
      return todayAttendanceData.value.clock_in && !todayAttendanceData.value.clock_out
    }
    return attendanceStore.isUserClockedIn
  })

  /**
   * Calculate today's work hours
   */
  const todayWorkHours = computed(() => {
    if (!todayAttendanceData.value) return '-'

    const attendance = todayAttendanceData.value
    return calculateWorkHours(attendance.clock_in, attendance.clock_out, attendance.work_duration)
  })

  /**
   * Format check-in time for display
   */
  const checkInTime = computed(() => {
    if (lastCheckInTime.value) return lastCheckInTime.value
    return parseTimeForDisplay(todayAttendanceData.value?.clock_in)
  })

  /**
   * Format check-out time for display
   */
  const checkOutTime = computed(() => {
    if (lastCheckOutTime.value) return lastCheckOutTime.value
    return parseTimeForDisplay(todayAttendanceData.value?.clock_out)
  })

  /**
   * Fetch today's attendance data
   */
  const fetchTodayAttendance = async (preserveClickTimes = false) => {
    if (!currentUser.value?.id) {
      console.log('❌ No user ID available for attendance fetch')
      todayAttendanceData.value = null
      return
    }

    console.log('🔄 Fetching attendance for user ID:', currentUser.value.id)
    loading.value = true

    try {
      const attendance = await AttendanceService.getUserTodayAttendance(currentUser.value.id)

      if (!attendance) {
        console.log('📅 No attendance record found for today')
        todayAttendanceData.value = null
        if (!preserveClickTimes) {
          resetClickTimes()
        }
      } else {
        todayAttendanceData.value = attendance

        if (!preserveClickTimes) {
          resetClickTimes()
        }

        console.log('✅ Successfully fetched attendance data:', {
          userId: currentUser.value.id,
          hasClockIn: !!attendance?.clock_in,
          hasClockOut: !!attendance?.clock_out,
          workDuration: attendance?.work_duration,
        })
      }
    } catch (error) {
      console.error("❌ Failed to fetch today's attendance:", error)
      todayAttendanceData.value = null
    } finally {
      loading.value = false
    }
  }

  /**
   * Reset captured click times
   */
  const resetClickTimes = () => {
    lastCheckInTime.value = null
    lastCheckOutTime.value = null
  }

  /**
   * Handle check-in action with location validation
   */
  const handleCheckIn = async () => {
    if (!currentUser.value?.id) return

    try {
      // Validate location first
      const locationResult = await locationValidation.validateCurrentLocation()

      if (!locationResult || !locationResult.isValid) {
        const errorMessage = locationResult?.message || 'ไม่สามารถระบุตำแหน่งได้'
        console.error('❌ Location validation failed:', errorMessage)
        throw new Error(errorMessage)
      }

      const now = new Date()
      lastCheckInTime.value = date.formatDate(now, DATE_FORMATS.TIME)

      // Include location data in check-in
      await attendanceStore.clockIn(
        currentUser.value.id,
        undefined, // note
        locationValidation.currentLocationForApi.value || undefined,
      )
      await fetchTodayAttendance(true) // Preserve captured time

      console.log('✅ Check-in successful with location validation')
    } catch (error) {
      lastCheckInTime.value = null
      console.error('❌ Check-in failed:', error)
      throw error
    }
  }

  /**
   * Handle check-out action with location validation
   */
  const handleCheckOut = async () => {
    if (!currentUser.value?.id) return

    try {
      // Validate location first
      const locationResult = await locationValidation.validateCurrentLocation()

      if (!locationResult || !locationResult.isValid) {
        const errorMessage = locationResult?.message || 'ไม่สามารถระบุตำแหน่งได้'
        console.error('❌ Location validation failed:', errorMessage)
        throw new Error(errorMessage)
      }

      const now = new Date()
      lastCheckOutTime.value = date.formatDate(now, DATE_FORMATS.TIME)

      // Include location data in check-out
      await attendanceStore.clockOut(
        currentUser.value.id,
        undefined, // note
        locationValidation.currentLocationForApi.value || undefined,
      )
      await fetchTodayAttendance(true) // Preserve captured time

      console.log('✅ Check-out successful with location validation')
    } catch (error) {
      lastCheckOutTime.value = null
      console.error('❌ Check-out failed:', error)
      throw error
    }
  }

  /**
   * Start periodic attendance refresh
   */
  const startPeriodicRefresh = () => {
    attendanceRefreshInterval.value = setInterval(() => {
      console.log('🔄 Periodic attendance refresh')
      fetchTodayAttendance(false).catch((error) => {
        console.error('❌ Periodic refresh failed:', error)
      })
    }, REFRESH_INTERVALS.ATTENDANCE_REFRESH)
  }

  /**
   * Stop periodic attendance refresh
   */
  const stopPeriodicRefresh = () => {
    if (attendanceRefreshInterval.value) {
      clearInterval(attendanceRefreshInterval.value)
      attendanceRefreshInterval.value = null
    }
  }

  /**
   * Initialize attendance functionality
   */
  const initialize = async () => {
    if (currentUser.value?.id) {
      await fetchTodayAttendance()
      startPeriodicRefresh()
    }
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    stopPeriodicRefresh()
  }

  // Watch for attendance data changes
  watch(
    todayAttendanceData,
    (newData) => {
      if (newData) {
        console.log('📊 Attendance data updated:', {
          clockIn: newData.clock_in,
          clockOut: newData.clock_out,
          workDuration: newData.work_duration,
        })
      }
    },
    { deep: true },
  )

  return {
    // State
    todayAttendanceData,
    loading,
    lastCheckInTime,
    lastCheckOutTime,

    // Computed
    isCheckedIn,
    todayWorkHours,
    checkInTime,
    checkOutTime,

    // Location validation
    locationValidation,

    // Methods
    fetchTodayAttendance,
    handleCheckIn,
    handleCheckOut,
    resetClickTimes,
    startPeriodicRefresh,
    stopPeriodicRefresh,
    initialize,
    cleanup,
  }
}
