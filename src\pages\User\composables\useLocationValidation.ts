/**
 * Location Validation Composable
 * Handles location validation for check-in/check-out functionality
 */

import { ref, computed } from 'vue'
import {
  GeolocationService,
  type GeolocationCoordinates,
  type GeolocationError,
} from 'src/services/geolocationService'
import { MAP_CONFIG } from '../constants'
import type { MapLocation } from '../types'

export interface LocationValidationResult {
  isValid: boolean
  distance: number
  message: string
}

export interface LocationValidationState {
  isLoading: boolean
  hasPermission: boolean
  currentLocation: GeolocationCoordinates | null
  lastError: GeolocationError | null
}

export const useLocationValidation = () => {
  // Reactive state
  const state = ref<LocationValidationState>({
    isLoading: false,
    hasPermission: false,
    currentLocation: null,
    lastError: null,
  })

  // Configuration
  const ALLOWED_LOCATION = MAP_CONFIG.DEFAULT_LOCATION
  const MAX_DISTANCE = 1000 // 1 kilometer in meters

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  const calculateDistance = (coord1: MapLocation, coord2: MapLocation): number => {
    const R = 6371e3 // Earth's radius in meters
    const φ1 = (coord1.lat * Math.PI) / 180
    const φ2 = (coord2.lat * Math.PI) / 180
    const Δφ = ((coord2.lat - coord1.lat) * Math.PI) / 180
    const Δλ = ((coord2.lng - coord1.lng) * Math.PI) / 180

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return Math.round(R * c)
  }

  /**
   * Format distance for display
   */
  const formatDistance = (distance: number): string => {
    if (distance < 1000) {
      return `${distance} เมตร`
    } else {
      return `${(distance / 1000).toFixed(1)} กิโลเมตร`
    }
  }

  /**
   * Validate location coordinates
   */
  const validateLocation = (userLocation: MapLocation): LocationValidationResult => {
    const distance = calculateDistance(userLocation, ALLOWED_LOCATION)
    const isValid = distance <= MAX_DISTANCE

    return {
      isValid,
      distance,
      message: isValid
        ? `ตำแหน่งถูกต้อง อยู่ห่างจากสถานที่ทำงาน ${formatDistance(distance)}`
        : `ตำแหน่งไม่ถูกต้อง คุณอยู่ห่างจากสถานที่ทำงาน ${formatDistance(distance)} (อนุญาตสูงสุด ${formatDistance(MAX_DISTANCE)})`,
    }
  }

  /**
   * Get current user location
   */
  const getCurrentLocation = async (): Promise<GeolocationCoordinates | null> => {
    state.value.isLoading = true
    state.value.lastError = null

    try {
      // Check if geolocation is supported
      if (!GeolocationService.isSupported()) {
        const error = new Error('เบราว์เซอร์ไม่รองรับการระบุตำแหน่ง') as Error & GeolocationError
        error.code = 0
        error.message = 'เบราว์เซอร์ไม่รองรับการระบุตำแหน่ง'
        throw error
      }

      // Request permission first
      const permission = await GeolocationService.requestPermission()
      state.value.hasPermission = permission === 'granted'

      // Get current position
      const coordinates = await GeolocationService.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 30000,
      })

      state.value.currentLocation = coordinates
      state.value.hasPermission = true
      return coordinates
    } catch (error) {
      const geoError = error as GeolocationError
      state.value.lastError = geoError
      state.value.hasPermission = false
      console.error('Location error:', geoError)
      return null
    } finally {
      state.value.isLoading = false
    }
  }

  /**
   * Validate current location for check-in/check-out
   */
  const validateCurrentLocation = async (): Promise<LocationValidationResult | null> => {
    const coordinates = await getCurrentLocation()

    if (!coordinates) {
      return {
        isValid: false,
        distance: -1,
        message: state.value.lastError
          ? GeolocationService.getErrorMessage(state.value.lastError)
          : 'ไม่สามารถระบุตำแหน่งได้',
      }
    }

    return validateLocation({
      lat: coordinates.latitude,
      lng: coordinates.longitude,
    })
  }

  /**
   * Clear location data
   */
  const clearLocation = () => {
    state.value.currentLocation = null
    state.value.lastError = null
    state.value.hasPermission = false
  }

  // Computed properties
  const isLocationAvailable = computed(() => state.value.currentLocation !== null)
  const hasLocationError = computed(() => state.value.lastError !== null)
  const locationErrorMessage = computed(() =>
    state.value.lastError ? GeolocationService.getErrorMessage(state.value.lastError) : null,
  )

  const currentLocationForApi = computed(() => {
    if (!state.value.currentLocation) return null

    return {
      latitude: state.value.currentLocation.latitude,
      longitude: state.value.currentLocation.longitude,
      accuracy: state.value.currentLocation.accuracy,
    }
  })

  return {
    // State
    state: computed(() => state.value),

    // Computed
    isLocationAvailable,
    hasLocationError,
    locationErrorMessage,
    currentLocationForApi,

    // Methods
    getCurrentLocation,
    validateCurrentLocation,
    validateLocation,
    calculateDistance,
    formatDistance,
    clearLocation,

    // Constants
    ALLOWED_LOCATION,
    MAX_DISTANCE,
  }
}
