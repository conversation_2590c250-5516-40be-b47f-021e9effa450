<template>
  <UserNavigation />
  <div class="q-pa-md">
    <div class="container q-pa-md">
      <div class="row q-col-gutter-md">
        <!-- Left Column - Map and Time -->
        <div class="col-12 col-md-6">
          <!-- Time Display -->
          <TimeDisplayCard
            :current-time="currentTime"
            :current-date="currentDate"
            class="q-mb-md"
          />

          <!-- Map Display -->
          <MapCard map-element-id="check-in-map" @location-reloaded="handleLocationReloaded" />
        </div>

        <!-- Right Column - Profile and Schedule -->
        <div class="col-12 col-md-6">
          <!-- Employee Profile -->
          <EmployeeProfileCard
            :employee-name="currentUser?.name || ''"
            :employee-role="currentUser?.role || ''"
            :employee-phone="currentUser?.tel || ''"
            :user-image-url="userImageUrl"
            class="q-mb-md"
          />

          <!-- Schedule Card -->
          <q-card flat class="schedule-card">
            <q-card-section>
              <div class="schedule-title">ตารางงาน</div>

              <!-- Week Days Display -->
              <WeekDaysDisplay :week-days="weekDays" />

              <!-- Work Hours Information -->
              <WorkHoursSection
                :total-hours="todayWorkHours"
                :check-in-time="checkInTime"
                :check-out-time="checkOutTime"
              />

              <!-- Check In/Out Actions -->
              <CheckInOutButtons
                :is-checked-in="Boolean(isCheckedIn)"
                :loading="loading"
                @check-in="handleCheckInWithNotify"
                @check-out="handleCheckOutWithNotify"
              />
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </div>
</template>

/** * CheckInOut Page Script * Manages employee check-in/check-out functionality with map and
schedule display */
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import UserNavigation from 'src/components/userNavigation.vue'
import { useUserStore } from 'src/stores/userStore'
import { useAuthStore } from 'src/stores/authStore'
import { UserService } from 'src/services/userService'

// Import new components
import TimeDisplayCard from './components/TimeDisplayCard.vue'
import MapCard from './components/MapCard.vue'
import EmployeeProfileCard from './components/EmployeeProfileCard.vue'
import WeekDaysDisplay from './components/WeekDaysDisplay.vue'
import WorkHoursSection from './components/WorkHoursSection.vue'
import CheckInOutButtons from './components/CheckInOutButtons.vue'

// Import composables
import { useDateTime } from './composables/useDateTime'
import { useAttendance } from './composables/useAttendance'

// Import types
import type { MapLocation } from './types'

// Store instances
const userStore = useUserStore()
const authStore = useAuthStore()

// Get current user from auth store (which persists across page refreshes)
const currentUser = computed(() => {
  return authStore.currentUser || userStore.currentUser
})

// Composables
const { currentTime, currentDate, weekDays } = useDateTime()

const {
  isCheckedIn,
  todayWorkHours,
  checkInTime,
  checkOutTime,
  loading,
  locationValidation,
  initialize: initializeAttendance,
  cleanup: cleanupAttendance,
  handleCheckIn,
  handleCheckOut,
} = useAttendance(currentUser)

// Quasar Notify
import { Notify } from 'quasar'

// Wrap check-in/out handlers to show notify
const handleCheckInWithNotify = async () => {
  try {
    await handleCheckIn()
    Notify.create({
      type: 'positive',
      message: 'เช็คอินสำเร็จ',
      position: 'bottom',
      timeout: 3000,
    })
  } catch (e) {
    const error = e as Error
    const isLocationError = error.message.includes('ตำแหน่ง') || error.message.includes('location')

    Notify.create({
      type: 'negative',
      message: isLocationError ? error.message : 'เช็คอินไม่สำเร็จ',
      position: 'bottom',
      timeout: isLocationError ? 5000 : 3000,
      actions: isLocationError
        ? [
            {
              label: 'ตกลง',
              color: 'white',
              handler: () => {
                // Dismiss notification
              },
            },
          ]
        : undefined,
    })
    throw e
  }
}

const handleCheckOutWithNotify = async () => {
  try {
    await handleCheckOut()
    Notify.create({
      type: 'positive',
      message: 'เช็คเอาท์สำเร็จ',
      position: 'bottom',
      timeout: 2000,
    })
  } catch (e) {
    Notify.create({
      type: 'negative',
      message: 'เช็คเอาท์ไม่สำเร็จ',
      position: 'bottom',
      timeout: 2000,
    })
    throw e
  }
}

defineExpose({ handleCheckInWithNotify, handleCheckOutWithNotify })

// Local state
const userImageUrl = ref<string | null>(null)

/**
 * Handle location reload event from MapCard
 */
const handleLocationReloaded = (location: MapLocation) => {
  console.log('📍 Location reloaded:', location)
}

/**
 * Load user image
 */
const loadUserImage = async () => {
  const userId = currentUser.value?.id
  if (userId) {
    try {
      const imageUrl = await UserService.getUserImageById(userId)
      userImageUrl.value = imageUrl
      console.log('✅ User image loaded successfully')
    } catch (error) {
      console.error('❌ Failed to load user image:', error)
    }
  }
}

// Watch for changes in current user to load image and initialize attendance
watch(
  currentUser,
  async (newUser) => {
    if (newUser?.id) {
      if (!userImageUrl.value) {
        await loadUserImage()
      }
      // Reinitialize attendance when user changes
      await initializeAttendance()
    }
  },
  { immediate: true },
)

/**
 * Initialize the page
 */
const initializePage = () => {
  // User image and attendance initialization are now handled by the watch
  console.log('📋 Page initialization complete')
}

/**
 * Cleanup function
 */
const cleanup = () => {
  cleanupAttendance()
}

// Lifecycle hooks
onMounted(() => {
  console.log('🚀 CheckInOut: Component mounted, initializing...')
  initializePage()
})

onUnmounted(() => {
  console.log('🧹 CheckInOut: Component unmounting, cleaning up...')
  cleanup()
})
</script>

<style scoped>
.time-display {
  font-size: 4rem;
  font-weight: bold;
}

.date-display {
  font-size: 1.2rem;
  margin-top: 8px;
}

/* Map Card */
.map-card {
  background: white;
  border-radius: 15px;
  min-height: 300px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.map-container {
  position: relative;
  height: 300px;
  border-radius: 15px;
  overflow: hidden;
}

.leaflet-map {
  width: 100%;
  height: 300px;
  border-radius: 15px;
  z-index: 1;
}

.location-info-card {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  max-width: 80%;
  z-index: 10;
}

.location-name {
  font-weight: bold;
  font-size: 1rem;
  color: #333;
  margin-bottom: 4px;
}

.location-service {
  font-size: 0.9rem;
  color: #666;
}

.map-controls {
  position: absolute;
  right: 10px;
  bottom: 80px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.map-control-btn {
  background: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .map-container {
    height: 250px;
  }

  .location-info-card {
    max-width: 90%;
  }
}

/* Profile Card */
.profile-card {
  background: rgba(209, 239, 232, 0.8);
  border-radius: 15px;
}

.employee-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.employee-detail {
  display: flex;
  align-items: center;
  color: #666;
  margin-bottom: 4px;
  font-size: 0.95rem;
}

/* Schedule Card */
.schedule-card {
  background: rgba(209, 239, 232, 0.8);
  border-radius: 15px;
}

.schedule-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
}

/* Week Days */
.week-days {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.day-item {
  flex: 1;
  text-align: center;
  padding: 12px 8px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.day-item.active-day {
  background: #91d2c1;
  color: white;
  transform: scale(1.05);
}

.day-letter {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
}

.day-number {
  font-size: 1.1rem;
  font-weight: bold;
}

/* Work Hours Section */
.work-hours-section {
  background: rgba(145, 210, 193, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.work-hours-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.work-hours-container {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.work-hours-item {
  flex: 1;
  text-align: center;
  background: rgba(145, 210, 193, 0.5);
  padding: 12px 8px;
  border-radius: 8px;
}

.hours-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 4px;
}

.hours-value {
  font-size: 0.95rem;
  font-weight: bold;
  color: #333;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 12px;
}

.check-out-btn {
  flex: 1;
  background: #9e9e9e;
  color: white;
  border-radius: 10px;
  font-weight: bold;
  padding: 12px;
  font-size: 1rem;
}

.check-out-btn:disabled {
  background: #e0e0e0;
  color: #bdbdbd;
}

.check-in-btn {
  flex: 1;
  background: #4caf50;
  color: white;
  border-radius: 10px;
  font-weight: bold;
  padding: 12px;
  font-size: 1rem;
}

.check-in-btn:disabled {
  background: #e0e0e0;
  color: #bdbdbd;
}

/* Responsive Design */
@media (max-width: 768px) {
  .time-display {
    font-size: 3rem;
  }

  .date-display {
    font-size: 1rem;
  }

  .map-container {
    height: 250px;
  }

  .work-hours-container {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
