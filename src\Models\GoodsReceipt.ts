import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { User } from "./User";
import { PurchaseOrder } from "./PurchaseOrder";
import { Supplier } from "./Supplier";
import { GoodsReceiptDetails } from "./GoodsReceiptDetails";
import { PaymentGoodsReceipt } from "./PaymentGoodsReceipt";
import { Branch } from "./Branch";


@Entity()
export class GoodsReceipt {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  code!: string;

  @ManyToOne(() => PurchaseOrder, { onDelete: "CASCADE" })
  @JoinColumn()
  po!: PurchaseOrder;

  @ManyToOne(() => Supplier, { onDelete: "CASCADE" }) //บริษัทจำหน่าย
  @JoinColumn()
  distributor!: Supplier;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  branch!: Branch;

  @Column({ type: "datetime", nullable: true })
  po_date!: Date;

  @Column({ type: "text", nullable: true })
  po_code!: string;

  @Column({ type: "datetime" })
  date_document!: Date; //วันที่เอกสาร

  @Column({ type: "datetime", nullable: true })
  receive_date!: Date; //วันที่รับสินค้า

  @Column({ type: "float", nullable: true })
  po_total!: number;

  @Column({ type: "float" }) //ภาษี
  tax!: number;

  @Column({ type: "float" }) //รวมภาษี
  tax_total!: number;

  @Column({ type: "float" }) //รวมเงินชำระ
  gr_total!: number;

  @Column({ type: "float", nullable: true }) //รวมเงิน ตรงรายการสินค้า
  gr_details_total!: number;

  @Column({ type: "text", nullable: true })
  tax_invoice_number!: string; //เลขที่ใบกำกับภาษี

  @Column({ type: 'datetime', nullable: true }) // วันที่ใบกำกับภาษี
  tax_invoice_date!: Date;

  @Column({ type: 'datetime', nullable: true }) // วันที่ให้เครดิต
  credit_date!: Date;

  @Column({ type: 'int', default: 30, nullable: true }) // จำนวนวันเครดิต 
  credit_days!: number;

  @Column({ type: "text" })
  status!: string;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  // @Column({ type: "text", nullable: true })
  // product_price_tax!: string;

  // @Column({ type: "text", nullable: true })
  // order_discount_tax!: string;
  @Column({ type: "boolean", default: true, nullable: true })
  is_tax_invoice!: boolean; // ใบรับสินค้ามีภาษีหรือไม่

  @Column({ type: "numeric", default: 0, nullable: true })
  order_discount!: number; // ส่วนลดท้ายบิล (บาท)

  @Column({ type: "text", default: 'ไม่รวมภาษี', nullable: true })
  product_price_tax!: string; // 'รวมภาษี' หรือ 'ไม่รวมภาษี' ราคารายการสินค้า

  @Column({ type: "numeric", default: 7.00, nullable: true })
  vat_percent!: number; // เปอร์เซ็นต์ภาษี เช่น 7

  @Column({ type: "boolean", default: true, nullable: true })
  is_manual_discount_before_tax!: boolean; // ติ๊กส่วนลดท้ายบิลก่อนภาษี (ถ้าสร้างเอง)

  @Column({ type: "text", default: 'ไม่รวมภาษี', nullable: true })
  order_discount_tax!: string; // 'ก่อนภาษี' หรือ 'หลังภาษี'

  @Column({ type: "text", default: '% ส่วนลด', nullable: true })
  is_before_tax_discount!: string; // ติ๊กส่วน % ส่วนลด ก่อนภาษี

  @Column({ type: "numeric", default: 0.0000, nullable: true })
  before_tax_discount_percent!: number; // กรณีระบุ % ส่วนลด

  @Column({ type: "numeric", default: 0.00, nullable: true })
  before_tax_discount_amount!: number; // กรณีระบุส่วนลดจำนวนเงิน

  @Column({ type: "boolean", default: true, nullable: true })
  is_manual_discount_after_tax!: boolean; // ติ๊กส่วนลดท้ายบิลหลังภาษี (ถ้าสร้างเอง)

  @Column({ type: "text", default: '% ส่วนลด', nullable: true })
  is_after_tax_discount!: string; // ติ๊กส่วน % ส่วนลด หลังภาษี

  @Column({ type: "numeric", default: 0.0000, nullable: true })
  after_tax_discount_percent!: number; // ส่วนลดท้ายบิลหลังภาษี กรณีระบุ % ส่วนลด

  @Column({ type: "numeric", default: 0.00, nullable: true })
  after_tax_discount_amount!: number; // กรณีระบุส่วนลดจำนวนเงิน

  @Column({ type: 'boolean', default: false, nullable: true })
  is_discount_applied!: boolean; //ใช้ส่วนลดในการคำนวณทุนสินค้า

  @Column({ type: 'boolean', default: false, nullable: true })
  is_tax_included!: boolean; //เพิ่มภาษีลงในราคาทุนสินค้า

  @Column({ type: 'text', nullable: true })
  note!: string;

  @OneToMany(() => GoodsReceiptDetails, (gr_details) => gr_details.gr)
  gr_details!: GoodsReceiptDetails[];

  @OneToMany(() => PaymentGoodsReceipt, (payment) => payment.gr)
  payment!: GoodsReceipt[];
}