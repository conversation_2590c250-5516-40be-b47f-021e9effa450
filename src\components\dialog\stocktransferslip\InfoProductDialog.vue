<!-- <template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1100px; width: 1100px"><q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดสินค้า</div>
          <q-btn icon="close" @click="closeDialog()" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-3 q-mt-md" style="margin-left: 10px; text-align: start">
                    สินค้า :
                  </div>
                  <div class="col-12 col-md-2">
                    <q-input class="input-container-mini" dense borderless
                      v-model="stock.formforGR.product.product_code" />
                  </div>
                  <div class="col-12 col-md-6">
                    <q-input class="input-container" dense borderless v-model="stock.formforGR.product.product_name"
                      type="text" />
                  </div>
                </div>
                <div class="row q-col-gutter-md" style="margin-top: 10px">
                  <div class="col-12 col-md-3 q-mt-md"
                    style="white-space: nowrap; margin-left: 10px; text-align: start">
                    จำนวนคงเหลือ :
                  </div>
                  <div class="col-12 col-md-3">
                    <q-input class="input-container" v-model="stock.formforGR.remaining" dense borderless />
                  </div>

                  <div class="col-12 col-md-2 q-mt-md">หน่วยฐาน</div>
                  <div class="col-12 col-md-3">
                    <q-input class="input-container" v-model="stock.formforGR.product.unit" dense borderless />
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-3 q-mt-md" style="text-align: start; white-space: nowrap">
                    หมายเหตุราคาทุน :
                  </div>
                  <div class="col-12 col-md-8" style="margin-left: 5px">
                    <q-input class="input-container" dense borderless v-model="stock.formforGR.product.cost_notes"
                      type="textarea" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายการสินค้า
          </div>
        </div>
      </q-card-section></q-card></q-dialog>
</template>
<script setup lang="ts">
import { useDialogGRDetails } from 'src/stores/dialog-gr-details'
import { useGoodsReceiptStore } from 'src/stores/goodsreceipt'
import { useStockStore } from 'src/stores/stock'
import ChangeUnitDialog from './ChangeUnitDialog.vue'

import { computed, watch } from 'vue'
import { date } from 'quasar'
import type { GoodsReceiptDetail } from 'src/types/goodsReceiptDatail'

const dialogPD = useDialogGRDetails()
const stock = useStockStore()
const store = useGoodsReceiptStore()
interface Props {
  modelValue: boolean
  grDetails?: GoodsReceiptDetail | null
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  grDetails: null,
  mode: 'edit'
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

watch(
  () => props.modelValue,
  () => {
    if (props.mode === 'edit' || props.mode === 'addProductList') {
      // store.grDetailsEdited = store.grDetails
    }
  }
)
const totalReceiveQty = computed(() => {
  const sum = store.formGoodsReceiptDetail.receive_quantity + store.formGoodsReceiptDetail.free_quantity;
  return sum
});
const totalReceiveAfterTax = computed(() => {
  const sum = store.formGoodsReceiptDetail.receive_quantity * store.formGoodsReceiptDetail.receive_price_before_tax;
  return Math.round(sum * 10000) / 10000;
});
const saveDetailsDialog = () => {
  store.formGoodsReceiptDetail.total_receive_quantity = totalReceiveQty.value
  store.formGoodsReceiptDetail.receive_price_after_discount = totalReceiveAfterTax.value
  store.formGoodsReceiptDetail.total_price_product = totalReceiveAfterTax.value
  // console.log(store.formGoodsReceiptDetail)
  const index = store.grDetails.findIndex(item => item.product.id === store.formGoodsReceiptDetail.product.id);

  if (props.mode === 'edit') {
    if (index !== -1) {
      store.grDetails[index] = {
        ...store.grDetails[index],
        ...store.formGoodsReceiptDetail
      };
    } else {
      store.grDetails.push(store.formGoodsReceiptDetail)
    }
    store.deletedIds = store.deletedIds.filter(id => id !== store.formGoodsReceiptDetail.product.id);
  } else {
    const exists = store.grDetailsEdited.some(item => item.product.id === store.formGoodsReceiptDetail.product.id);
    if (!exists) {
      store.grDetailsEdited.push(store.formGoodsReceiptDetail);
    }
    // else{
    //   store.grDetailsEdited[index] = {
    //     ...originalItem,
    //     ...copiedFormOrderItems,
    //     id: oldId, // ป้องกัน id หาย
    //     quantity: updatedQuantity
    //   };
    // }
  }
  store.form.gr_details_total = store.grDetails.reduce((acc, item) => acc + item.total_price_product, 0)
  store.formGoodsReceiptDetail.gr_total = store.grDetails.reduce((acc, item) => acc + item.total_price_product, 0)
  isOpen.value = false
  store.resetFormGRDetails()
}
const closeDialog = () => {
  isOpen.value = false
  store.resetFormGRDetails()
  store.resetFormLatestGRDetail()
}

function OpenChangeUnit() {
  dialogPD.openCU('addCU')
  console.log('เปิดหน้าเปลี่ยนหน่วย')
}
const formattedMfgDate = computed({
  get() {
    const dateValue = store.formGoodsReceiptDetail.mfg_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formGoodsReceiptDetail.mfg_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formGoodsReceiptDetail.mfg_date = new Date(value);
    }
  },
});
const formattedExpDate = computed({
  get() {
    const dateValue = store.formGoodsReceiptDetail.exp_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formGoodsReceiptDetail.exp_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formGoodsReceiptDetail.exp_date = new Date(value);
    }
  },
});
const formattedLastestExpDate = computed({
  get() {
    const dateValue = store.formLatestGRDetail.exp_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formLatestGRDetail.exp_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formLatestGRDetail.exp_date = new Date(value);
    }
  },
});
const formattedLastestMfgDate = computed({
  get() {
    const dateValue = store.formLatestGRDetail.mfg_date;
    return dateValue ? date.formatDate(dateValue, 'DD/MM/YYYY') : '';
  },
  set(value: string) {
    // ถ้าค่า value เป็น string ว่าง แสดงว่าให้เคลียร์ค่า
    if (value === '') {
      store.formLatestGRDetail.mfg_date = new Date(''); // หรือ undefined ก็ได้ ถ้า model รองรับ
    } else {
      store.formLatestGRDetail.mfg_date = new Date(value);
    }
  },
});
</script>
<style scoped>
.gap-container {
  margin-bottom: 20px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
  font-size: 15px;
}

.input-container-2 {
  padding-left: 5px;
  padding-right: 5px;
  background-color: #d9d9d9;
  border-radius: 5px;
  font-size: 13px;
  height: 30px;
  margin-left: 5px;
}

.input-container-mini {
  padding-left: 10px;
  padding-right: 10px;
  background-color: #a4cafe;
  border-radius: 5px;
  display: flex;
  align-items: center;
  /* จัดกลางแนวตั้ง */
  justify-content: center;
  /* จัดกลางแนวนอน */
}

.mini-container-header {
  background-color: #83a7d8;
  border-radius: 5px 5px 0 0;
  width: 490px;
  height: 45px;
}

.width-column {
  width: 100px;
}

.container-headerhalf {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  background-color: white;
  border-radius: 0 0 5px 5px;
  width: 490px;
  padding-bottom: 10px;
}

.btn-add-2 {
  background-color: #294888;
  color: white;
  border-radius: 3px;
  margin-left: 10px;
  font-size: 13px;
  margin-top: 17px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
  width: 80px;
  color: white;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
  width: 80px;
  color: white;
}
</style> -->
