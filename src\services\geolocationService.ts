/**
 * Geolocation Service
 * Handles browser geolocation API for getting user's current location
 */

export interface GeolocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

export interface GeolocationError {
  code: number;
  message: string;
}

export interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
}

export class GeolocationService {
  private static readonly DEFAULT_OPTIONS: GeolocationOptions = {
    enableHighAccuracy: true,
    timeout: 10000, // 10 seconds
    maximumAge: 60000, // 1 minute
  };

  /**
   * Check if geolocation is supported by the browser
   * @returns True if geolocation is supported
   */
  public static isSupported(): boolean {
    return 'geolocation' in navigator;
  }

  /**
   * Get current position using browser geolocation API
   * @param options Geolocation options
   * @returns Promise with current coordinates
   */
  public static async getCurrentPosition(
    options?: GeolocationOptions
  ): Promise<GeolocationCoordinates> {
    return new Promise((resolve, reject) => {
      if (!this.isSupported()) {
        reject({
          code: 0,
          message: 'Geolocation is not supported by this browser',
        } as GeolocationError);
        return;
      }

      const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp,
          });
        },
        (error) => {
          let message = 'Unknown geolocation error';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              message = 'Location access denied by user';
              break;
            case error.POSITION_UNAVAILABLE:
              message = 'Location information is unavailable';
              break;
            case error.TIMEOUT:
              message = 'Location request timed out';
              break;
          }

          reject({
            code: error.code,
            message,
          } as GeolocationError);
        },
        finalOptions
      );
    });
  }

  /**
   * Watch position changes (for real-time tracking)
   * @param callback Function to call when position changes
   * @param errorCallback Function to call on error
   * @param options Geolocation options
   * @returns Watch ID that can be used to clear the watch
   */
  public static watchPosition(
    callback: (coordinates: GeolocationCoordinates) => void,
    errorCallback?: (error: GeolocationError) => void,
    options?: GeolocationOptions
  ): number | null {
    if (!this.isSupported()) {
      if (errorCallback) {
        errorCallback({
          code: 0,
          message: 'Geolocation is not supported by this browser',
        });
      }
      return null;
    }

    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };

    return navigator.geolocation.watchPosition(
      (position) => {
        callback({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        });
      },
      (error) => {
        if (errorCallback) {
          let message = 'Unknown geolocation error';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              message = 'Location access denied by user';
              break;
            case error.POSITION_UNAVAILABLE:
              message = 'Location information is unavailable';
              break;
            case error.TIMEOUT:
              message = 'Location request timed out';
              break;
          }

          errorCallback({
            code: error.code,
            message,
          });
        }
      },
      finalOptions
    );
  }

  /**
   * Clear position watch
   * @param watchId Watch ID returned by watchPosition
   */
  public static clearWatch(watchId: number): void {
    if (this.isSupported()) {
      navigator.geolocation.clearWatch(watchId);
    }
  }

  /**
   * Request permission for geolocation (for browsers that support it)
   * @returns Promise with permission state
   */
  public static async requestPermission(): Promise<PermissionState> {
    if ('permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        return permission.state;
      } catch (error) {
        console.warn('Permission API not supported:', error);
        return 'prompt';
      }
    }
    return 'prompt';
  }

  /**
   * Get user-friendly error message
   * @param error Geolocation error
   * @returns User-friendly error message
   */
  public static getErrorMessage(error: GeolocationError): string {
    switch (error.code) {
      case 1: // PERMISSION_DENIED
        return 'กรุณาอนุญาตการเข้าถึงตำแหน่งในเบราว์เซอร์เพื่อใช้งานระบบเช็คอิน/เช็คเอาท์';
      case 2: // POSITION_UNAVAILABLE
        return 'ไม่สามารถระบุตำแหน่งได้ กรุณาตรวจสอบการเชื่อมต่อ GPS หรือ Wi-Fi';
      case 3: // TIMEOUT
        return 'การระบุตำแหน่งใช้เวลานานเกินไป กรุณาลองใหม่อีกครั้ง';
      default:
        return 'เกิดข้อผิดพลาดในการระบุตำแหน่ง กรุณาลองใหม่อีกครั้ง';
    }
  }
}
