import type { Branch } from './branch'
import type { PurchaseOrderItem } from './purchaseOrderitem'
import type { Supplier } from './supplier'
import type { user } from './user'
export interface PurchaseOrder {
  id: number
  code: string
  supplier: Supplier
  branch: Branch
  contact: string
  address: string
  date: Date
  user: user
  po_total: number
  tax: number
  tax_total: number
  status: string
  order_date: Date
  order_discount: number
  note: string
  order_total: number
  receive_total: number
  product_price_tax: string
  order_discount_tax: string
  receive_status: string
  purchase_order_item: PurchaseOrderItem[]
}
