/**
 * Composable for Leaflet map management
 */

import { ref, nextTick } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import { generateLocationOffset } from '../utils'
import { MAP_CONFIG } from '../constants'
import type { MapLocation } from '../types'

// Custom red icon for marker (fallback to default if not loaded)
const redIcon = new L.Icon({
  iconUrl:
    'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
})

// Radar animation state
let radarCircle: L.Circle | null = null
let radarAnimationId: number | null = null

// Location validation elements
let allowedAreaCircle: L.Circle | null = null
let userLocationMarker: L.Marker | null = null

export const useLeafletMap = () => {
  // Reactive state
  const mapLocation = ref<MapLocation>({ ...MAP_CONFIG.DEFAULT_LOCATION })
  let leafletMap: unknown = null
  let leafletMarker: unknown = null

  /**
   * Initialize Leaflet map
   */
  const initializeMap = async (mapElementId: string) => {
    await nextTick()

    const mapDiv = document.getElementById(mapElementId)
    if (!mapDiv || leafletMap) return

    try {
      // Create map instance
      leafletMap = L.map(mapDiv).setView(
        [mapLocation.value.lat, mapLocation.value.lng],
        MAP_CONFIG.DEFAULT_ZOOM,
      )

      // Add tile layer
      L.tileLayer(MAP_CONFIG.TILE_LAYER_URL, {
        attribution: MAP_CONFIG.ATTRIBUTION,
      }).addTo(leafletMap as L.Map)

      // Add marker with red icon, fallback to default if error
      try {
        leafletMarker = L.marker([mapLocation.value.lat, mapLocation.value.lng], {
          icon: redIcon,
        }).addTo(leafletMap as L.Map)
      } catch {
        leafletMarker = L.marker([mapLocation.value.lat, mapLocation.value.lng]).addTo(
          leafletMap as L.Map,
        )
      }

      // Add radar circle
      addRadarEffect(mapLocation.value.lat, mapLocation.value.lng)

      // Add allowed area circle (1km radius)
      addAllowedAreaCircle()

      console.log('✅ Leaflet map initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Leaflet map:', error)
    }
  }

  /**
   * Update map view and marker position
   */
  const updateMapView = (newLocation: MapLocation) => {
    if (!leafletMap || !leafletMarker) return

    try {
      const latlng = [newLocation.lat, newLocation.lng] as [number, number]
      ;(leafletMap as L.Map).setView(latlng, MAP_CONFIG.DEFAULT_ZOOM)
      ;(leafletMarker as L.Marker).setLatLng(latlng)

      // Move radar effect
      addRadarEffect(newLocation.lat, newLocation.lng)

      mapLocation.value = { ...newLocation }
      console.log('📍 Map view updated to:', newLocation)
    } catch (error) {
      console.error('❌ Failed to update map view:', error)
    }
  }

  /**
   * Add allowed area circle (1km radius)
   */
  const addAllowedAreaCircle = () => {
    if (!leafletMap) return

    // Remove existing allowed area circle
    if (allowedAreaCircle) {
      ;(leafletMap as L.Map).removeLayer(allowedAreaCircle)
      allowedAreaCircle = null
    }

    // Create allowed area circle (1000m radius)
    allowedAreaCircle = L.circle(
      [MAP_CONFIG.DEFAULT_LOCATION.lat, MAP_CONFIG.DEFAULT_LOCATION.lng],
      {
        color: '#4CAF50',
        fillColor: '#4CAF50',
        fillOpacity: 0.1,
        radius: 1000, // 1 kilometer
        weight: 2,
        dashArray: '5, 5',
      },
    ).addTo(leafletMap as L.Map)

    // Add popup to explain the allowed area
    allowedAreaCircle.bindPopup(
      '<div style="text-align: center;"><strong>พื้นที่อนุญาต</strong><br/>รัศมี 1 กิโลเมตร<br/>สำหรับเช็คอิน/เช็คเอาท์</div>',
    )
  }

  // Radar animation logic
  function addRadarEffect(lat: number, lng: number) {
    // Remove previous circle if exists
    if (radarCircle && leafletMap) {
      ;(leafletMap as L.Map).removeLayer(radarCircle)
      radarCircle = null
    }
    // Cancel previous animation
    if (radarAnimationId) {
      cancelAnimationFrame(radarAnimationId)
      radarAnimationId = null
    }
    // Create new circle
    radarCircle = L.circle([lat, lng], {
      color: undefined, // No border
      fillColor: 'red',
      fillOpacity: 0.3,
      radius: 30,
      interactive: false,
      weight: 0, // No border
      opacity: 0, // No border
    }).addTo(leafletMap as L.Map)
    // Animate
    let start: number | null = null
    const duration = 2200 // ms (slower radar expand)
    const minRadius = 30
    const maxRadius = 120
    function animateRadar(ts: number) {
      if (!start) start = ts
      const elapsed = ts - start
      let progress = (elapsed % duration) / duration
      // Ease out
      progress = 1 - Math.pow(1 - progress, 2)
      const radius = minRadius + (maxRadius - minRadius) * progress
      const opacity = 0.3 * (1 - progress)
      if (radarCircle) {
        radarCircle.setRadius(radius)
        radarCircle.setStyle({ fillOpacity: opacity })
      }
      radarAnimationId = requestAnimationFrame(animateRadar)
    }
    radarAnimationId = requestAnimationFrame(animateRadar)
  }

  /**
   * Reload location with random offset (simulation)
   */
  const reloadLocation = () => {
    const newLocation = generateLocationOffset(
      MAP_CONFIG.DEFAULT_LOCATION,
      MAP_CONFIG.LOCATION_OFFSET_RANGE,
    )

    updateMapView(newLocation)
    console.log('🔄 Location reloaded with offset')
  }

  /**
   * Get current location (placeholder for real geolocation)
   */
  const getCurrentLocation = (): MapLocation => {
    // In a real application, this would use the browser's geolocation API
    // For now, return the default location with a small offset
    return generateLocationOffset(MAP_CONFIG.DEFAULT_LOCATION, MAP_CONFIG.LOCATION_OFFSET_RANGE)
  }

  /**
   * Set map location
   */
  const setLocation = (location: MapLocation) => {
    updateMapView(location)
  }

  /**
   * Show user's current location on map
   */
  const showUserLocation = (userLat: number, userLng: number, isValid: boolean) => {
    if (!leafletMap) return

    // Remove existing user location marker
    if (userLocationMarker) {
      ;(leafletMap as L.Map).removeLayer(userLocationMarker)
      userLocationMarker = null
    }

    // Create user location marker with appropriate color
    const markerColor = isValid ? 'green' : 'red'
    const markerIcon = new L.Icon({
      iconUrl: `https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-${markerColor}.png`,
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41],
    })

    userLocationMarker = L.marker([userLat, userLng], {
      icon: markerIcon,
    }).addTo(leafletMap as L.Map)

    // Add popup with validation status
    const statusText = isValid ? 'ตำแหน่งถูกต้อง' : 'ตำแหน่งไม่ถูกต้อง'
    const statusColor = isValid ? '#4CAF50' : '#F44336'
    userLocationMarker.bindPopup(
      `<div style="text-align: center; color: ${statusColor};"><strong>${statusText}</strong><br/>ตำแหน่งปัจจุบันของคุณ</div>`,
    )
  }

  /**
   * Destroy map instance
   */
  const destroyMap = () => {
    if (leafletMap) {
      try {
        // Remove radar animation
        if (radarAnimationId) {
          cancelAnimationFrame(radarAnimationId)
          radarAnimationId = null
        }
        if (radarCircle && leafletMap) {
          ;(leafletMap as L.Map).removeLayer(radarCircle)
          radarCircle = null
        }
        // Remove allowed area circle
        if (allowedAreaCircle && leafletMap) {
          ;(leafletMap as L.Map).removeLayer(allowedAreaCircle)
          allowedAreaCircle = null
        }
        // Remove user location marker
        if (userLocationMarker && leafletMap) {
          ;(leafletMap as L.Map).removeLayer(userLocationMarker)
          userLocationMarker = null
        }
        ;(leafletMap as L.Map).remove()
        leafletMap = null
        leafletMarker = null
        console.log('🧹 Leaflet map destroyed')
      } catch (error) {
        console.error('❌ Error destroying map:', error)
      }
    }
  }

  /**
   * Check if map is initialized
   */
  const isMapInitialized = (): boolean => {
    return leafletMap !== null
  }

  return {
    // State
    mapLocation,

    // Methods
    initializeMap,
    updateMapView,
    reloadLocation,
    getCurrentLocation,
    setLocation,
    showUserLocation,
    destroyMap,
    isMapInitialized,
  }
}
